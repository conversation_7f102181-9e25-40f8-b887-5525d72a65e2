<template>
  <div class="p-6">
    <!-- Calendar Header -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-4">
        <button
          @click="previousMonth"
          class="p-2 rounded-md hover:bg-gray-100 transition-colors"
        >
          <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h2 class="text-xl font-semibold text-gray-900">
          {{ currentMonthName }} {{ currentYear }}
        </h2>
        <button
          @click="nextMonth"
          class="p-2 rounded-md hover:bg-gray-100 transition-colors"
        >
          <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      <button
        @click="goToToday"
        class="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
      >
        Today
      </button>
    </div>

    <!-- Calendar Legend -->
    <div class="flex items-center space-x-6 mb-4 text-sm">
      <div class="flex items-center space-x-2">
        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
        <span class="text-gray-600">Events</span>
      </div>
      <div class="flex items-center space-x-2">
        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
        <span class="text-gray-600">Equipment Expiry</span>
      </div>
      <div class="flex items-center space-x-2">
        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
        <span class="text-gray-600">Reminders</span>
      </div>
    </div>

    <!-- Calendar Grid -->
    <div class="grid grid-cols-7 gap-px bg-gray-200 rounded-lg overflow-hidden">
      <!-- Day Headers -->
      <div
        v-for="day in dayHeaders"
        :key="day"
        class="bg-gray-50 p-3 text-center text-sm font-medium text-gray-700"
      >
        {{ day }}
      </div>

      <!-- Calendar Days -->
      <div
        v-for="day in calendarDays"
        :key="`${day.date.getTime()}`"
        class="bg-white min-h-[120px] p-2 relative"
        :class="{
          'bg-gray-50': !day.isCurrentMonth,
          'bg-blue-50': day.isToday
        }"
      >
        <!-- Day Number -->
        <div class="flex items-center justify-between mb-1">
          <span
            class="text-sm font-medium"
            :class="{
              'text-gray-400': !day.isCurrentMonth,
              'text-blue-600 font-bold': day.isToday,
              'text-gray-900': day.isCurrentMonth && !day.isToday
            }"
          >
            {{ day.date.getDate() }}
          </span>
        </div>

        <!-- Calendar Items -->
        <div class="space-y-1">
          <div
            v-for="item in day.items.slice(0, 3)"
            :key="item.id"
            class="text-xs p-1 rounded truncate cursor-pointer"
            :class="{
              'bg-blue-100 text-blue-800': item.color === 'blue',
              'bg-red-100 text-red-800': item.color === 'red',
              'bg-green-100 text-green-800': item.color === 'green'
            }"
            @click="showItemDetails(item)"
          >
            {{ item.title }}
          </div>
          
          <!-- More items indicator -->
          <div
            v-if="day.items.length > 3"
            class="text-xs text-gray-500 cursor-pointer hover:text-gray-700"
            @click="showDayDetails(day)"
          >
            +{{ day.items.length - 3 }} more
          </div>
        </div>
      </div>
    </div>

    <!-- Day Details Modal -->
    <DayDetailsModal
      v-if="selectedDay"
      :day="selectedDay"
      @close="selectedDay = null"
    />

    <!-- Item Details Modal -->
    <ItemDetailsModal
      v-if="selectedItem"
      :item="selectedItem"
      @close="selectedItem = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useEventsStore } from '@/stores/events'
import type { CalendarItem } from '@/types'
import DayDetailsModal from '@/components/modals/DayDetailsModal.vue'
import ItemDetailsModal from '@/components/modals/ItemDetailsModal.vue'

interface CalendarDay {
  date: Date
  isCurrentMonth: boolean
  isToday: boolean
  items: CalendarItem[]
}

const eventsStore = useEventsStore()

const currentDate = ref(new Date())
const selectedDay = ref<CalendarDay | null>(null)
const selectedItem = ref<CalendarItem | null>(null)

const currentYear = computed(() => currentDate.value.getFullYear())
const currentMonth = computed(() => currentDate.value.getMonth())

const currentMonthName = computed(() => {
  return currentDate.value.toLocaleDateString('en-US', { month: 'long' })
})

const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

const calendarDays = computed(() => {
  const year = currentYear.value
  const month = currentMonth.value
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)
  const endDate = new Date(lastDay)

  // Adjust to start on Sunday
  startDate.setDate(startDate.getDate() - startDate.getDay())
  
  // Adjust to end on Saturday
  endDate.setDate(endDate.getDate() + (6 - endDate.getDay()))

  const days: CalendarDay[] = []
  const currentDateObj = new Date(startDate)
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  while (currentDateObj <= endDate) {
    const dayDate = new Date(currentDateObj)
    const isCurrentMonth = dayDate.getMonth() === month
    const isToday = dayDate.getTime() === today.getTime()
    
    // Get calendar items for this day
    const items = eventsStore.getCalendarItems(year, month).filter(item => {
      const itemDate = new Date(item.date)
      return itemDate.getDate() === dayDate.getDate() &&
             itemDate.getMonth() === dayDate.getMonth() &&
             itemDate.getFullYear() === dayDate.getFullYear()
    })

    days.push({
      date: dayDate,
      isCurrentMonth,
      isToday,
      items
    })

    currentDateObj.setDate(currentDateObj.getDate() + 1)
  }

  return days
})

const previousMonth = () => {
  currentDate.value = new Date(currentYear.value, currentMonth.value - 1, 1)
}

const nextMonth = () => {
  currentDate.value = new Date(currentYear.value, currentMonth.value + 1, 1)
}

const goToToday = () => {
  currentDate.value = new Date()
}

const showDayDetails = (day: CalendarDay) => {
  selectedDay.value = day
}

const showItemDetails = (item: CalendarItem) => {
  selectedItem.value = item
}

onMounted(() => {
  // Initialize sample data when component mounts
  eventsStore.initializeSampleData()
})
</script>
