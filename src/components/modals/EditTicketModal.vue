<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">Edit Ticket</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-4">
          <div>
            <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter ticket title"
            />
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description *</label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Describe the issue or request"
            ></textarea>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
              <select
                id="priority"
                v-model="form.priority"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>

            <div>
              <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                id="status"
                v-model="form.status"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="open">Open</option>
                <option value="in-progress">In Progress</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
              </select>
            </div>
          </div>

          <div>
            <label for="customerId" class="block text-sm font-medium text-gray-700 mb-1">Customer</label>
            <select
              id="customerId"
              v-model="form.customerId"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a customer (optional)</option>
              <option
                v-for="customer in customersStore.customers"
                :key="customer.id"
                :value="customer.id"
              >
                {{ customer.name }} - {{ customer.company || 'No Company' }}
              </option>
            </select>
          </div>

          <div>
            <label for="equipmentId" class="block text-sm font-medium text-gray-700 mb-1">Equipment</label>
            <select
              id="equipmentId"
              v-model="form.equipmentId"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select equipment (optional)</option>
              <option
                v-for="equipment in availableEquipment"
                :key="equipment.id"
                :value="equipment.id"
              >
                {{ equipment.name }} ({{ equipment.model }})
              </option>
            </select>
          </div>

          <div>
            <label for="assignedTo" class="block text-sm font-medium text-gray-700 mb-1">Assigned To</label>
            <input
              id="assignedTo"
              v-model="form.assignedTo"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter assignee name"
            />
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
            >
              Update Ticket
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTicketsStore } from '@/stores/tickets'
import { useCustomersStore } from '@/stores/customers'
import { useEquipmentStore } from '@/stores/equipment'
import type { Ticket } from '@/types'

const props = defineProps<{
  ticket: Ticket
}>()

const emit = defineEmits<{
  close: []
}>()

const ticketsStore = useTicketsStore()
const customersStore = useCustomersStore()
const equipmentStore = useEquipmentStore()

const form = ref({
  title: '',
  description: '',
  priority: 'medium' as Ticket['priority'],
  status: 'open' as Ticket['status'],
  customerId: '',
  equipmentId: '',
  assignedTo: ''
})

const availableEquipment = computed(() => {
  if (form.value.customerId) {
    return equipmentStore.getEquipmentByCustomer(form.value.customerId)
  }
  return equipmentStore.equipment
})

const handleSubmit = () => {
  const updates = {
    title: form.value.title,
    description: form.value.description,
    status: form.value.status,
    priority: form.value.priority,
    customerId: form.value.customerId || undefined,
    equipmentId: form.value.equipmentId || undefined,
    assignedTo: form.value.assignedTo || undefined
  }

  ticketsStore.updateTicket(props.ticket.id, updates)
  emit('close')
}

onMounted(() => {
  // Initialize form with ticket data
  form.value = {
    title: props.ticket.title,
    description: props.ticket.description,
    priority: props.ticket.priority,
    status: props.ticket.status,
    customerId: props.ticket.customerId || '',
    equipmentId: props.ticket.equipmentId || '',
    assignedTo: props.ticket.assignedTo || ''
  }
  
  // Initialize data if needed
  customersStore.initializeSampleData()
  equipmentStore.initializeSampleData()
})
</script>
