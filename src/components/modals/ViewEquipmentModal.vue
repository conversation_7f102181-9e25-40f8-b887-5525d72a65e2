<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <div class="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">{{ equipment.name }}</h3>
              <p class="text-sm text-gray-500">Equipment Details</p>
            </div>
          </div>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="space-y-6">
          <!-- Basic Information -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Basic Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg space-y-3">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-xs font-medium text-gray-500 mb-1">Model</label>
                  <p class="text-sm text-gray-900">{{ equipment.model }}</p>
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-500 mb-1">Serial Number</label>
                  <p class="text-sm text-gray-900 font-mono">{{ equipment.serialNumber }}</p>
                </div>
              </div>
              <div>
                <label class="block text-xs font-medium text-gray-500 mb-1">Status</label>
                <span
                  class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': equipment.status === 'active',
                    'bg-red-100 text-red-800': equipment.status === 'expired',
                    'bg-yellow-100 text-yellow-800': equipment.status === 'maintenance'
                  }"
                >
                  {{ equipment.status }}
                </span>
              </div>
              <div v-if="equipment.description">
                <label class="block text-xs font-medium text-gray-500 mb-1">Description</label>
                <p class="text-sm text-gray-900">{{ equipment.description }}</p>
              </div>
            </div>
          </div>

          <!-- Customer Information -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Customer Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div v-if="customerInfo" class="flex items-center space-x-3">
                <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                  <span class="text-xs font-medium text-white">
                    {{ customerInfo.name.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ customerInfo.name }}</p>
                  <p class="text-xs text-gray-500">{{ customerInfo.company || 'No Company' }}</p>
                </div>
              </div>
              <div v-else class="text-sm text-gray-500">Customer information not available</div>
            </div>
          </div>

          <!-- Date Information -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Date Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg space-y-3">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-xs font-medium text-gray-500 mb-1">Purchase Date</label>
                  <p class="text-sm text-gray-900">{{ formatDate(equipment.purchaseDate) }}</p>
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-500 mb-1">Expiry Date</label>
                  <p
                    class="text-sm font-medium"
                    :class="{
                      'text-red-600': isExpired(equipment.expiryDate),
                      'text-yellow-600': isExpiringSoon(equipment.expiryDate),
                      'text-gray-900': !isExpired(equipment.expiryDate) && !isExpiringSoon(equipment.expiryDate)
                    }"
                  >
                    {{ formatDate(equipment.expiryDate) }}
                  </p>
                  <p
                    class="text-xs mt-1"
                    :class="{
                      'text-red-600': isExpired(equipment.expiryDate),
                      'text-yellow-600': isExpiringSoon(equipment.expiryDate),
                      'text-gray-500': !isExpired(equipment.expiryDate) && !isExpiringSoon(equipment.expiryDate)
                    }"
                  >
                    {{ getExpiryStatus(equipment.expiryDate) }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- System Information -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">System Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Equipment ID:</span>
                <span class="text-gray-900 font-mono">{{ equipment.id }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Created:</span>
                <span class="text-gray-900">{{ formatDate(equipment.createdAt) }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Last Updated:</span>
                <span class="text-gray-900">{{ formatDate(equipment.updatedAt) }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-6">
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
          <button
            @click="$emit('edit', equipment)"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
          >
            Edit Equipment
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useCustomersStore } from '@/stores/customers'
import type { Equipment } from '@/types'

const props = defineProps<{
  equipment: Equipment
}>()

const emit = defineEmits<{
  close: []
  edit: [equipment: Equipment]
}>()

const customersStore = useCustomersStore()

const customerInfo = computed(() => {
  return customersStore.getCustomerById(props.equipment.customerId)
})

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const isExpired = (date: Date) => {
  return new Date(date) < new Date()
}

const isExpiringSoon = (date: Date) => {
  const now = new Date()
  const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
  const expiryDate = new Date(date)
  return expiryDate >= now && expiryDate <= thirtyDaysFromNow
}

const getExpiryStatus = (date: Date) => {
  if (isExpired(date)) {
    const daysExpired = Math.floor((new Date().getTime() - new Date(date).getTime()) / (1000 * 60 * 60 * 24))
    return `Expired ${daysExpired} days ago`
  } else if (isExpiringSoon(date)) {
    const daysUntilExpiry = Math.floor((new Date(date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    return `Expires in ${daysUntilExpiry} days`
  }
  return 'Active'
}
</script>
