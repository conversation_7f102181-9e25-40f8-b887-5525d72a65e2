<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <div class="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center">
              <span class="text-lg font-medium text-white">
                {{ customer.name.charAt(0).toUpperCase() }}
              </span>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">{{ customer.name }}</h3>
              <p class="text-sm text-gray-500">Customer Details</p>
            </div>
          </div>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="space-y-6">
          <!-- Contact Information -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Contact Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg space-y-3">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span class="text-sm text-gray-900">{{ customer.email }}</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                <span class="text-sm text-gray-900">{{ customer.phone }}</span>
              </div>
              <div class="flex items-start space-x-3">
                <svg class="w-5 h-5 text-gray-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span class="text-sm text-gray-900">{{ customer.address }}</span>
              </div>
            </div>
          </div>

          <!-- Company Information -->
          <div v-if="customer.company">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Company Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <span class="text-sm text-gray-900">{{ customer.company }}</span>
              </div>
            </div>
          </div>

          <!-- Account Information -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Account Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Customer ID:</span>
                <span class="text-gray-900 font-mono">{{ customer.id }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Created:</span>
                <span class="text-gray-900">{{ formatDate(customer.createdAt) }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Last Updated:</span>
                <span class="text-gray-900">{{ formatDate(customer.updatedAt) }}</span>
              </div>
            </div>
          </div>

          <!-- Equipment Summary -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Equipment Summary</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Total Equipment:</span>
                <span class="text-gray-900 font-medium">{{ customerEquipment.length }}</span>
              </div>
              <div v-if="customerEquipment.length > 0" class="mt-2">
                <div class="text-xs text-gray-500 mb-1">Recent Equipment:</div>
                <div class="space-y-1">
                  <div
                    v-for="equipment in customerEquipment.slice(0, 3)"
                    :key="equipment.id"
                    class="text-xs text-gray-700"
                  >
                    {{ equipment.name }} ({{ equipment.model }})
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-6">
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
          <button
            @click="$emit('edit', customer)"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
          >
            Edit Customer
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useEquipmentStore } from '@/stores/equipment'
import type { Customer } from '@/types'

const props = defineProps<{
  customer: Customer
}>()

const emit = defineEmits<{
  close: []
  edit: [customer: Customer]
}>()

const equipmentStore = useEquipmentStore()

const customerEquipment = computed(() => {
  return equipmentStore.getEquipmentByCustomer(props.customer.id)
})

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>
