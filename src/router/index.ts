import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '@/views/Dashboard.vue'
import Customers from '@/views/Customers.vue'
import Equipment from '@/views/Equipment.vue'
import Tickets from '@/views/Tickets.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'dashboard',
      component: Dashboard,
      meta: {
        title: 'Dashboard'
      }
    },
    {
      path: '/customers',
      name: 'customers',
      component: Customers,
      meta: {
        title: 'Customers'
      }
    },
    {
      path: '/equipment',
      name: 'equipment',
      component: Equipment,
      meta: {
        title: 'Equipment'
      }
    },
    {
      path: '/tickets',
      name: 'tickets',
      component: Tickets,
      meta: {
        title: 'Tickets'
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      redirect: '/'
    }
  ],
})

// Navigation guard to set page title
router.beforeEach((to) => {
  if (to.meta.title) {
    document.title = `${to.meta.title} - JNX Manager`
  }
})

export default router
