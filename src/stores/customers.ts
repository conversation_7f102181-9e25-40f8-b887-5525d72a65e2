import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Customer } from '@/types'

export const useCustomersStore = defineStore('customers', () => {
  const customers = ref<Customer[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getCustomerById = computed(() => {
    return (id: string) => customers.value.find(customer => customer.id === id)
  })

  const totalCustomers = computed(() => customers.value.length)

  // Actions
  const addCustomer = (customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newCustomer: Customer = {
      ...customerData,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    customers.value.push(newCustomer)
    return newCustomer
  }

  const updateCustomer = (id: string, updates: Partial<Omit<Customer, 'id' | 'createdAt'>>) => {
    const index = customers.value.findIndex(customer => customer.id === id)
    if (index !== -1) {
      customers.value[index] = {
        ...customers.value[index],
        ...updates,
        updatedAt: new Date()
      }
      return customers.value[index]
    }
    return null
  }

  const deleteCustomer = (id: string) => {
    const index = customers.value.findIndex(customer => customer.id === id)
    if (index !== -1) {
      customers.value.splice(index, 1)
      return true
    }
    return false
  }

  const searchCustomers = (query: string) => {
    const lowercaseQuery = query.toLowerCase()
    return customers.value.filter(customer =>
      customer.name.toLowerCase().includes(lowercaseQuery) ||
      customer.email.toLowerCase().includes(lowercaseQuery) ||
      customer.company?.toLowerCase().includes(lowercaseQuery)
    )
  }

  // Helper function to generate IDs
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // Initialize with sample data
  const initializeSampleData = () => {
    if (customers.value.length === 0) {
      const sampleCustomers: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>[] = [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '******-0123',
          address: '123 Main St, Anytown, USA',
          company: 'Acme Corp'
        },
        {
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '******-0456',
          address: '456 Oak Ave, Somewhere, USA',
          company: 'Tech Solutions Inc'
        }
      ]
      
      sampleCustomers.forEach(customer => addCustomer(customer))
    }
  }

  return {
    customers,
    loading,
    error,
    getCustomerById,
    totalCustomers,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    searchCustomers,
    initializeSampleData
  }
})
