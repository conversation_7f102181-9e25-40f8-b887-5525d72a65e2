import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Equipment } from '@/types'

export const useEquipmentStore = defineStore('equipment', () => {
  const equipment = ref<Equipment[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getEquipmentById = computed(() => {
    return (id: string) => equipment.value.find(item => item.id === id)
  })

  const getEquipmentByCustomer = computed(() => {
    return (customerId: string) => equipment.value.filter(item => item.customerId === customerId)
  })

  const expiredEquipment = computed(() => {
    const now = new Date()
    return equipment.value.filter(item => new Date(item.expiryDate) < now)
  })

  const expiringEquipment = computed(() => {
    const now = new Date()
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
    return equipment.value.filter(item => {
      const expiryDate = new Date(item.expiryDate)
      return expiryDate >= now && expiryDate <= thirtyDaysFromNow
    })
  })

  const totalEquipment = computed(() => equipment.value.length)

  // Actions
  const addEquipment = (equipmentData: Omit<Equipment, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newEquipment: Equipment = {
      ...equipmentData,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    equipment.value.push(newEquipment)
    return newEquipment
  }

  const updateEquipment = (id: string, updates: Partial<Omit<Equipment, 'id' | 'createdAt'>>) => {
    const index = equipment.value.findIndex(item => item.id === id)
    if (index !== -1) {
      equipment.value[index] = {
        ...equipment.value[index],
        ...updates,
        updatedAt: new Date()
      }
      return equipment.value[index]
    }
    return null
  }

  const deleteEquipment = (id: string) => {
    const index = equipment.value.findIndex(item => item.id === id)
    if (index !== -1) {
      equipment.value.splice(index, 1)
      return true
    }
    return false
  }

  const searchEquipment = (query: string) => {
    const lowercaseQuery = query.toLowerCase()
    return equipment.value.filter(item =>
      item.name.toLowerCase().includes(lowercaseQuery) ||
      item.model.toLowerCase().includes(lowercaseQuery) ||
      item.serialNumber.toLowerCase().includes(lowercaseQuery)
    )
  }

  // Helper function to generate IDs
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // Initialize with sample data
  const initializeSampleData = () => {
    if (equipment.value.length === 0) {
      const sampleEquipment: Omit<Equipment, 'id' | 'createdAt' | 'updatedAt'>[] = [
        {
          name: 'Laptop Dell XPS 13',
          model: 'XPS 13 9310',
          serialNumber: 'DL123456789',
          customerId: 'sample-customer-1',
          purchaseDate: new Date('2023-01-15'),
          expiryDate: new Date('2026-01-15'),
          status: 'active',
          description: 'High-performance laptop for development work'
        },
        {
          name: 'Server HP ProLiant',
          model: 'ProLiant DL380',
          serialNumber: 'HP987654321',
          customerId: 'sample-customer-2',
          purchaseDate: new Date('2022-06-01'),
          expiryDate: new Date('2025-06-01'),
          status: 'active',
          description: 'Main server for hosting applications'
        }
      ]
      
      sampleEquipment.forEach(item => addEquipment(item))
    }
  }

  return {
    equipment,
    loading,
    error,
    getEquipmentById,
    getEquipmentByCustomer,
    expiredEquipment,
    expiringEquipment,
    totalEquipment,
    addEquipment,
    updateEquipment,
    deleteEquipment,
    searchEquipment,
    initializeSampleData
  }
})
