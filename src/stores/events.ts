import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Event, CalendarItem } from '@/types'
import { useEquipmentStore } from './equipment'

export const useEventsStore = defineStore('events', () => {
  const events = ref<Event[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getEventById = computed(() => {
    return (id: string) => events.value.find(event => event.id === id)
  })

  const getEventsByDate = computed(() => {
    return (date: Date) => {
      const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
      return events.value.filter(event => {
        const eventDate = new Date(event.date.getFullYear(), event.date.getMonth(), event.date.getDate())
        return eventDate.getTime() === targetDate.getTime()
      })
    }
  })

  const getEventsByMonth = computed(() => {
    return (year: number, month: number) => {
      return events.value.filter(event => {
        const eventDate = new Date(event.date)
        return eventDate.getFullYear() === year && eventDate.getMonth() === month
      })
    }
  })

  const upcomingEvents = computed(() => {
    const now = new Date()
    return events.value
      .filter(event => new Date(event.date) >= now)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(0, 5)
  })

  // Get calendar items including equipment expiry dates
  const getCalendarItems = computed(() => {
    return (year: number, month: number): CalendarItem[] => {
      const equipmentStore = useEquipmentStore()
      const calendarItems: CalendarItem[] = []

      // Add regular events
      const monthEvents = getEventsByMonth.value(year, month)
      monthEvents.forEach(event => {
        calendarItems.push({
          id: event.id,
          title: event.title,
          date: event.date,
          type: event.type,
          color: event.color,
          relatedId: event.equipmentId || event.customerId
        })
      })

      // Add equipment expiry events
      equipmentStore.equipment.forEach(equipment => {
        const expiryDate = new Date(equipment.expiryDate)
        if (expiryDate.getFullYear() === year && expiryDate.getMonth() === month) {
          calendarItems.push({
            id: `expiry-${equipment.id}`,
            title: `${equipment.name} expires`,
            date: expiryDate,
            type: 'equipment-expiry',
            color: 'red',
            relatedId: equipment.id
          })
        }
      })

      return calendarItems.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    }
  })

  // Actions
  const addEvent = (eventData: Omit<Event, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newEvent: Event = {
      ...eventData,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    events.value.push(newEvent)
    return newEvent
  }

  const updateEvent = (id: string, updates: Partial<Omit<Event, 'id' | 'createdAt'>>) => {
    const index = events.value.findIndex(event => event.id === id)
    if (index !== -1) {
      events.value[index] = {
        ...events.value[index],
        ...updates,
        updatedAt: new Date()
      }
      return events.value[index]
    }
    return null
  }

  const deleteEvent = (id: string) => {
    const index = events.value.findIndex(event => event.id === id)
    if (index !== -1) {
      events.value.splice(index, 1)
      return true
    }
    return false
  }

  // Helper function to generate IDs
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // Initialize with sample data
  const initializeSampleData = () => {
    if (events.value.length === 0) {
      const sampleEvents: Omit<Event, 'id' | 'createdAt' | 'updatedAt'>[] = [
        {
          title: 'Team Meeting',
          description: 'Weekly team sync meeting',
          date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
          type: 'event',
          color: 'blue'
        },
        {
          title: 'System Maintenance',
          description: 'Scheduled system maintenance window',
          date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
          type: 'reminder',
          color: 'green'
        }
      ]
      
      sampleEvents.forEach(event => addEvent(event))
    }
  }

  return {
    events,
    loading,
    error,
    getEventById,
    getEventsByDate,
    getEventsByMonth,
    upcomingEvents,
    getCalendarItems,
    addEvent,
    updateEvent,
    deleteEvent,
    initializeSampleData
  }
})
