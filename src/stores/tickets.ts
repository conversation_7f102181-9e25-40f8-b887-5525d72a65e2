import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Ticket } from '@/types'

export const useTicketsStore = defineStore('tickets', () => {
  const tickets = ref<Ticket[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getTicketById = computed(() => {
    return (id: string) => tickets.value.find(ticket => ticket.id === id)
  })

  const getTicketsByCustomer = computed(() => {
    return (customerId: string) => tickets.value.filter(ticket => ticket.customerId === customerId)
  })

  const getTicketsByStatus = computed(() => {
    return (status: Ticket['status']) => tickets.value.filter(ticket => ticket.status === status)
  })

  const getTicketsByPriority = computed(() => {
    return (priority: Ticket['priority']) => tickets.value.filter(ticket => ticket.priority === priority)
  })

  const openTickets = computed(() => {
    return tickets.value.filter(ticket => ticket.status === 'open' || ticket.status === 'in-progress')
  })

  const urgentTickets = computed(() => {
    return tickets.value.filter(ticket => ticket.priority === 'urgent' && ticket.status !== 'closed')
  })

  const recentTickets = computed(() => {
    return tickets.value
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 10)
  })

  const ticketStats = computed(() => {
    const stats = {
      total: tickets.value.length,
      open: 0,
      inProgress: 0,
      resolved: 0,
      closed: 0,
      urgent: 0,
      high: 0,
      medium: 0,
      low: 0
    }

    tickets.value.forEach(ticket => {
      // Status counts
      switch (ticket.status) {
        case 'open':
          stats.open++
          break
        case 'in-progress':
          stats.inProgress++
          break
        case 'resolved':
          stats.resolved++
          break
        case 'closed':
          stats.closed++
          break
      }

      // Priority counts
      switch (ticket.priority) {
        case 'urgent':
          stats.urgent++
          break
        case 'high':
          stats.high++
          break
        case 'medium':
          stats.medium++
          break
        case 'low':
          stats.low++
          break
      }
    })

    return stats
  })

  // Actions
  const addTicket = (ticketData: Omit<Ticket, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newTicket: Ticket = {
      ...ticketData,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    tickets.value.push(newTicket)
    return newTicket
  }

  const updateTicket = (id: string, updates: Partial<Omit<Ticket, 'id' | 'createdAt'>>) => {
    const index = tickets.value.findIndex(ticket => ticket.id === id)
    if (index !== -1) {
      const updatedTicket = {
        ...tickets.value[index],
        ...updates,
        updatedAt: new Date()
      }

      // Set resolvedAt when status changes to resolved
      if (updates.status === 'resolved' && tickets.value[index].status !== 'resolved') {
        updatedTicket.resolvedAt = new Date()
      }

      tickets.value[index] = updatedTicket
      return updatedTicket
    }
    return null
  }

  const deleteTicket = (id: string) => {
    const index = tickets.value.findIndex(ticket => ticket.id === id)
    if (index !== -1) {
      tickets.value.splice(index, 1)
      return true
    }
    return false
  }

  const searchTickets = (query: string) => {
    const lowercaseQuery = query.toLowerCase()
    return tickets.value.filter(ticket =>
      ticket.title.toLowerCase().includes(lowercaseQuery) ||
      ticket.description.toLowerCase().includes(lowercaseQuery) ||
      ticket.status.toLowerCase().includes(lowercaseQuery) ||
      ticket.priority.toLowerCase().includes(lowercaseQuery)
    )
  }

  // Helper function to generate IDs
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // Initialize with sample data
  const initializeSampleData = () => {
    if (tickets.value.length === 0) {
      const sampleTickets: Omit<Ticket, 'id' | 'createdAt' | 'updatedAt'>[] = [
        {
          title: 'Login Issues',
          description: 'User cannot log into the system after password reset',
          status: 'open',
          priority: 'high',
          customerId: 'sample-customer-1',
          createdBy: 'admin'
        },
        {
          title: 'Equipment Maintenance Request',
          description: 'Server requires routine maintenance and updates',
          status: 'in-progress',
          priority: 'medium',
          equipmentId: 'sample-equipment-1',
          assignedTo: 'tech-support',
          createdBy: 'admin'
        },
        {
          title: 'Software License Renewal',
          description: 'Need to renew software licenses before expiration',
          status: 'resolved',
          priority: 'low',
          createdBy: 'admin',
          resolvedAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
        }
      ]
      
      sampleTickets.forEach(ticket => addTicket(ticket))
    }
  }

  return {
    tickets,
    loading,
    error,
    getTicketById,
    getTicketsByCustomer,
    getTicketsByStatus,
    getTicketsByPriority,
    openTickets,
    urgentTickets,
    recentTickets,
    ticketStats,
    addTicket,
    updateTicket,
    deleteTicket,
    searchTickets,
    initializeSampleData
  }
})
