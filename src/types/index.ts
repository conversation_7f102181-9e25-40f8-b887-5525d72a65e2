export interface Customer {
  id: string
  name: string
  email: string
  phone: string
  address: string
  company?: string
  createdAt: Date
  updatedAt: Date
}

export interface Equipment {
  id: string
  name: string
  model: string
  serialNumber: string
  customerId: string
  purchaseDate: Date
  expiryDate: Date
  status: 'active' | 'expired' | 'maintenance'
  description?: string
  createdAt: Date
  updatedAt: Date
}

export interface Event {
  id: string
  title: string
  description: string
  date: Date
  type: 'event' | 'equipment-expiry' | 'reminder'
  color: 'blue' | 'red' | 'green'
  equipmentId?: string
  customerId?: string
  createdAt: Date
  updatedAt: Date
}

export interface Ticket {
  id: string
  title: string
  description: string
  status: 'open' | 'in-progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  customerId?: string
  equipmentId?: string
  assignedTo?: string
  createdBy: string
  createdAt: Date
  updatedAt: Date
  resolvedAt?: Date
}

export interface CalendarItem {
  id: string
  title: string
  date: Date
  type: 'event' | 'equipment-expiry' | 'reminder'
  color: 'blue' | 'red' | 'green'
  relatedId?: string
}
