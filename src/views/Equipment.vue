<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">Equipment</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
      >
        Add Equipment
      </button>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Active Equipment</p>
            <p class="text-2xl font-semibold text-gray-900">{{ equipmentStore.totalEquipment }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 rounded-lg">
            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Expiring Soon</p>
            <p class="text-2xl font-semibold text-gray-900">{{ equipmentStore.expiringEquipment.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 rounded-lg">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Expired</p>
            <p class="text-2xl font-semibold text-gray-900">{{ equipmentStore.expiredEquipment.length }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 rounded-lg shadow">
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search equipment..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <select
          v-model="statusFilter"
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="expired">Expired</option>
          <option value="maintenance">Maintenance</option>
        </select>
        <button
          @click="clearFilters"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
        >
          Clear
        </button>
      </div>
    </div>

    <!-- Equipment Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          All Equipment ({{ filteredEquipment.length }})
        </h3>
      </div>

      <div v-if="filteredEquipment.length === 0" class="p-6 text-center text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <p class="mt-2">No equipment found</p>
        <p class="text-sm text-gray-400">Get started by adding your first equipment</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Equipment
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Expiry Date
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="equipment in filteredEquipment"
              :key="equipment.id"
              class="hover:bg-gray-50 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ equipment.name }}</div>
                  <div class="text-sm text-gray-500">{{ equipment.model }} • {{ equipment.serialNumber }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getCustomerName(equipment.customerId) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': equipment.status === 'active',
                    'bg-red-100 text-red-800': equipment.status === 'expired',
                    'bg-yellow-100 text-yellow-800': equipment.status === 'maintenance'
                  }"
                >
                  {{ equipment.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ formatDate(equipment.expiryDate) }}</div>
                <div
                  class="text-xs"
                  :class="{
                    'text-red-600': isExpired(equipment.expiryDate),
                    'text-yellow-600': isExpiringSoon(equipment.expiryDate),
                    'text-gray-500': !isExpired(equipment.expiryDate) && !isExpiringSoon(equipment.expiryDate)
                  }"
                >
                  {{ getExpiryStatus(equipment.expiryDate) }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="viewEquipment(equipment)"
                    class="text-blue-600 hover:text-blue-900 transition-colors"
                  >
                    View
                  </button>
                  <button
                    @click="editEquipment(equipment)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors"
                  >
                    Edit
                  </button>
                  <button
                    @click="deleteEquipment(equipment)"
                    class="text-red-600 hover:text-red-900 transition-colors"
                  >
                    Delete
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add Equipment Modal -->
    <AddEquipmentModal v-if="showAddModal" @close="showAddModal = false" />

    <!-- Edit Equipment Modal -->
    <EditEquipmentModal
      v-if="showEditModal && selectedEquipment"
      :equipment="selectedEquipment"
      @close="showEditModal = false"
    />

    <!-- View Equipment Modal -->
    <ViewEquipmentModal
      v-if="showViewModal && selectedEquipment"
      :equipment="selectedEquipment"
      @close="showViewModal = false"
      @edit="editEquipment"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useEquipmentStore } from '@/stores/equipment'
import { useCustomersStore } from '@/stores/customers'
import type { Equipment } from '@/types'
import AddEquipmentModal from '@/components/modals/AddEquipmentModal.vue'
import EditEquipmentModal from '@/components/modals/EditEquipmentModal.vue'
import ViewEquipmentModal from '@/components/modals/ViewEquipmentModal.vue'

const equipmentStore = useEquipmentStore()
const customersStore = useCustomersStore()

const searchQuery = ref('')
const statusFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const selectedEquipment = ref<Equipment | null>(null)

const filteredEquipment = computed(() => {
  let equipment = equipmentStore.equipment

  // Apply search filter
  if (searchQuery.value) {
    equipment = equipmentStore.searchEquipment(searchQuery.value)
  }

  // Apply status filter
  if (statusFilter.value) {
    equipment = equipment.filter(item => item.status === statusFilter.value)
  }

  return equipment
})

const getCustomerName = (customerId: string) => {
  const customer = customersStore.getCustomerById(customerId)
  return customer?.name || 'Unknown Customer'
}

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const isExpired = (date: Date) => {
  return new Date(date) < new Date()
}

const isExpiringSoon = (date: Date) => {
  const now = new Date()
  const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
  const expiryDate = new Date(date)
  return expiryDate >= now && expiryDate <= thirtyDaysFromNow
}

const getExpiryStatus = (date: Date) => {
  if (isExpired(date)) {
    return 'Expired'
  } else if (isExpiringSoon(date)) {
    return 'Expires soon'
  }
  return 'Active'
}

const clearFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
}

const viewEquipment = (equipment: Equipment) => {
  selectedEquipment.value = equipment
  showViewModal.value = true
}

const editEquipment = (equipment: Equipment) => {
  selectedEquipment.value = equipment
  showEditModal.value = true
  showViewModal.value = false
}

const deleteEquipment = (equipment: Equipment) => {
  if (confirm(`Are you sure you want to delete ${equipment.name}?`)) {
    equipmentStore.deleteEquipment(equipment.id)
  }
}

onMounted(() => {
  equipmentStore.initializeSampleData()
  customersStore.initializeSampleData()
})
</script>
